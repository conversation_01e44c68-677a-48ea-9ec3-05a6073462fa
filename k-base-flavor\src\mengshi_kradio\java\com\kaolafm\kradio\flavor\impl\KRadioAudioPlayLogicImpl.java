package com.kaolafm.kradio.flavor.impl;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Looper;
import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioAudioPlayLogicInter;
import com.kaolafm.kradio.lib.utils.IntentUtils;
import com.kaolafm.kradio.player.helper.PlayerManagerHelper;
import com.kaolafm.opensdk.player.core.listener.OnPlayLogicListener;
import com.kaolafm.opensdk.player.core.utils.PlayerCustomizeManager;
import com.kaolafm.opensdk.player.logic.PlayerManager;

/**
 * 东风猛士专用音频播放逻辑实现
 * 主要解决多屏切换时播放状态异常的问题
 */
public final class KRadioAudioPlayLogicImpl implements KRadioAudioPlayLogicInter {
    private final static String TAG = "MengshiAudioPlayLogic";

    @SuppressLint("LongLogTag")
    public KRadioAudioPlayLogicImpl() {
        PlayerCustomizeManager.getInstance().setPlayLogicListener(() -> {
            int currentFocus = PlayerManager.getInstance().getCurrentAudioFocusStatus();
            Log.i(TAG, "isAppOnForeground--------->currentFocus = " + currentFocus);
            if ((currentFocus < 0)) {
                boolean isAppOnForeground = IntentUtils.getInstance().isAppOnForeground();
                Log.i(TAG, "isAppOnForeground--------->isAppOnForeground = " + isAppOnForeground);
                return !isAppOnForeground;
            }
            return false;
        });
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean autoPlayAudio(Object... args) {
        return false;
    }

    @Override
    public boolean requestAudioFocus(Object... args) {
        boolean flag = PlayerManager.getInstance().requestAudioFocus();
        Log.i(TAG, "requestAudioFocus result: " + flag);
        return flag;
    }

    @Override
    public boolean resumeAudioPlayLogic(Object... args) {
        Log.i(TAG, "resumeAudioPlayLogic called");

        // 超简单方案：直接检查播放器状态，如果有播放项但未播放，则恢复播放
        PlayerManager playerManager = PlayerManager.getInstance();
        if (playerManager.getCurPlayItem() != null && !playerManager.isPlaying() && !playerManager.isPauseFromUser()) {
            Log.i(TAG, "resumeAudioPlayLogic: auto-resuming playback after activity restart");

            // 延迟恢复播放，确保Activity完全就绪
            new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                try {
                    if (playerManager.getCurrentAudioFocusStatus() <= 0) {
                        requestAudioFocus();
                    }
                    PlayerManagerHelper.getInstance().switchPlayerStatus(false);
                    Log.i(TAG, "resumeAudioPlayLogic: playback resumed successfully");
                } catch (Exception e) {
                    Log.e(TAG, "resumeAudioPlayLogic: failed to resume", e);
                }
            }, 200);

            return true;
        }

        // 正常的恢复逻辑
        recoverPlay(false);
        return true;
    }

    @SuppressLint("LongLogTag")
    @Override
    public boolean restartAudioPlayLogic(Object... args) {
        return false;
    }

    /**
     * 恢复播放逻辑
     * 针对多屏切换场景进行优化
     * 
     * @param autoPlay 是否是自动播放请求
     */
    @SuppressLint("LongLogTag")
    private void recoverPlay(boolean autoPlay) {
        PlayerManager playerManager = PlayerManager.getInstance();
        
        Log.i(TAG, "recoverPlay start - autoPlay: " + autoPlay + 
                   ", isPauseFromUser: " + playerManager.isPauseFromUser() + 
                   ", isPlaying: " + playerManager.isPlaying() + 
                   ", audioFocusStatus: " + playerManager.getCurrentAudioFocusStatus());
        
        // 如果是用户主动暂停的，只处理音频焦点，不自动恢复播放
        if (playerManager.isPauseFromUser()) {
            Log.i(TAG, "recoverPlay: user paused, only handle audio focus");
            if (playerManager.getCurrentAudioFocusStatus() < 0) {
                requestAudioFocus();
            }
            return;
        }
        
        // 如果已经有音频焦点且正在播放，无需处理
        if (playerManager.getCurrentAudioFocusStatus() > 0 && playerManager.isPlaying()) {
            Log.i(TAG, "recoverPlay: already playing with audio focus");
            return;
        }
        
        // 请求音频焦点
        if (playerManager.getCurrentAudioFocusStatus() <= 0) {
            Log.i(TAG, "recoverPlay: requesting audio focus");
            requestAudioFocus();
        }
        
        // 如果不是正在播放状态，且不是用户暂停的，则恢复播放
        // 这里是关键：在多屏切换时，即使播放器被系统暂停，也要恢复播放
        if (!playerManager.isPlaying()) {
            Log.i(TAG, "recoverPlay: switching player status to resume playback");
            
            // 使用主线程执行，确保UI状态同步
            if (Looper.myLooper() == Looper.getMainLooper()) {
                PlayerManagerHelper.getInstance().switchPlayerStatus(false);
            } else {
                // 如果不在主线程，切换到主线程执行
                android.os.Handler mainHandler = new android.os.Handler(Looper.getMainLooper());
                mainHandler.post(() -> {
                    Log.i(TAG, "recoverPlay: executing switchPlayerStatus on main thread");
                    PlayerManagerHelper.getInstance().switchPlayerStatus(false);
                });
            }
        }
    }

    @Override
    public boolean doStartInPlay(Object... args) {
        return false;
    }


}
