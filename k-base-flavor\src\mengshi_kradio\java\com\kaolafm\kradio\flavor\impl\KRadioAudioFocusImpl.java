package com.kaolafm.kradio.flavor.impl;

import android.content.Context;
import android.media.AudioManager;
import android.util.Log;

import com.kaolafm.kradio.lib.base.AppDelegate;
import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusInter;

/**
 * 东风猛士专用音频焦点管理实现
 * 提供标准的音频焦点申请和释放功能
 */
public final class KRadioAudioFocusImpl implements KRadioAudioFocusInter {
    private static final String TAG = "MengshiAudioFocus";

    @Override
    public boolean requestAudioFocus(Object... args) {
        try {
            AudioManager am = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
            if (am == null) {
                Log.e(TAG, "requestAudioFocus: AudioManager is null");
                return false;
            }

            if (args.length > 0 && args[0] instanceof AudioManager.OnAudioFocusChangeListener) {
                AudioManager.OnAudioFocusChangeListener listener = (AudioManager.OnAudioFocusChangeListener) args[0];
                int result = am.requestAudioFocus(listener, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN);
                boolean status = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
                Log.i(TAG, "requestAudioFocus result: " + result + ", status: " + status);
                return status;
            } else {
                Log.e(TAG, "requestAudioFocus: invalid arguments");
                return false;
            }
        } catch (Exception e) {
            Log.e(TAG, "requestAudioFocus: exception", e);
            return false;
        }
    }

    @Override
    public boolean abandonAudioFocus(Object... args) {
        try {
            AudioManager am = (AudioManager) AppDelegate.getInstance().getContext().getSystemService(Context.AUDIO_SERVICE);
            if (am == null) {
                Log.e(TAG, "abandonAudioFocus: AudioManager is null");
                return false;
            }

            if (args.length > 0 && args[0] instanceof AudioManager.OnAudioFocusChangeListener) {
                AudioManager.OnAudioFocusChangeListener listener = (AudioManager.OnAudioFocusChangeListener) args[0];
                int result = am.abandonAudioFocus(listener);
                boolean status = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED;
                Log.i(TAG, "abandonAudioFocus result: " + result + ", status: " + status);
                return status;
            } else {
                Log.e(TAG, "abandonAudioFocus: invalid arguments");
                return false;
            }
        } catch (Exception e) {
            Log.e(TAG, "abandonAudioFocus: exception", e);
            return false;
        }
    }
}
