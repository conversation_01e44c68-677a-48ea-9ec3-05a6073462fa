package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusListenerInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import static android.media.AudioManager.AUDIOFOCUS_GAIN;
import static android.media.AudioManager.AUDIOFOCUS_LOSS;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT;

/**
 * 东风猛士专用音频焦点监听器实现
 * 针对多屏切换场景进行优化，避免不必要的播放中断
 */
public class KRadioAudioFocusListenerImpl implements KRadioAudioFocusListenerInter {
    private static final String TAG = "MengshiAudioFocusListener";

    @Override
    public void onFocusChanged(int status) {
        Log.d(TAG, "onFocusChanged: " + status);
        
        switch (status) {
            case AUDIOFOCUS_LOSS:
            case AUDIOFOCUS_LOSS_TRANSIENT:
                handleAudioFocusLoss(status);
                break;
            case AUDIOFOCUS_GAIN:
                handleAudioFocusGain();
                break;
            default:
                Log.d(TAG, "onFocusChanged: unhandled status " + status);
                break;
        }
    }

    /**
     * 处理音频焦点丢失
     * 在多屏切换场景下，需要谨慎处理焦点丢失，避免误暂停
     */
    private void handleAudioFocusLoss(int status) {
        PlayerManager playerManager = PlayerManager.getInstance();
        
        Log.d(TAG, "handleAudioFocusLoss: status=" + status + 
                   ", isPlaying=" + playerManager.isPlaying() + 
                   ", isPauseFromUser=" + playerManager.isPauseFromUser());
        
        if (playerManager.isPlaying()) {
            Log.d(TAG, "handleAudioFocusLoss: pausing playback due to focus loss");
            // 标记为非用户暂停，这样在恢复时可以自动播放
            playerManager.pause(false);
        } else {
            // 如果已经暂停，确保暂停状态正确标记
            boolean isPauseFromUser = playerManager.isPauseFromUser();
            Log.d(TAG, "handleAudioFocusLoss: already paused, isPauseFromUser=" + isPauseFromUser);
            // 重新调用pause确保状态一致
            playerManager.pause(isPauseFromUser);
        }
    }

    /**
     * 处理音频焦点获得
     * 在多屏切换场景下，如果之前不是用户主动暂停，则自动恢复播放
     */
    private void handleAudioFocusGain() {
        PlayerManager playerManager = PlayerManager.getInstance();
        
        Log.d(TAG, "handleAudioFocusGain: isPlaying=" + playerManager.isPlaying() + 
                   ", isPauseFromUser=" + playerManager.isPauseFromUser());
        
        // 只有在非用户暂停且当前未播放的情况下才自动恢复
        if (!playerManager.isPlaying() && !playerManager.isPauseFromUser()) {
            try {
                // 检查是否有有效的播放项
                if (playerManager.getCurPlayItem() != null) {
                    Log.d(TAG, "handleAudioFocusGain: resuming playback");
                    playerManager.play();
                } else {
                    Log.d(TAG, "handleAudioFocusGain: no valid play item to resume");
                }
            } catch (Exception e) {
                Log.e(TAG, "handleAudioFocusGain: failed to resume playback", e);
            }
        } else {
            Log.d(TAG, "handleAudioFocusGain: not resuming - already playing or user paused");
        }
    }
}
